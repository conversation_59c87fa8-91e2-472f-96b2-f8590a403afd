import { defineStore } from 'pinia'
import { ref, computed, nextTick, watch } from 'vue'
import {
  getRulesStatus,
  getRuleDetail,
  getRuleSchema,
  getRuleStatistics,
  downloadRuleTemplate,
  confirmRuleSubmission
} from '../api/rules'
import { ElMessage } from 'element-plus'
import { useAsyncState } from '@/composables/core/useAsyncState'
import { useStateMachine } from '@/composables/core/useStateMachine'
import { useFeedback } from '@/composables/ui/useFeedback'
import { useAppStore } from './app'

/**
 * 规则管理Store
 * 使用Composition API语法，提供规则相关的状态管理和操作方法
 * 集成企业级状态管理架构，提供智能缓存和错误恢复
 */
export const useRulesStore = defineStore('rules', () => {
  // ==================== 依赖注入 ====================

  const appStore = useAppStore()
  const feedback = useFeedback()
  const stateMachine = useStateMachine()

  // ==================== 增强状态定义 ====================

  // 规则列表相关状态
  const rules = ref([])
  const loading = ref(false)
  const lastFetchTime = ref(null)

  // 规则详情相关状态
  const currentRule = ref(null)
  const ruleSchema = ref([])
  const ruleStatistics = ref({})
  const detailLoading = ref(false)

  // 增强缓存和性能优化状态
  const rulesCache = ref(new Map())
  const schemaCache = ref(new Map())
  const statsCache = ref(new Map())

  // 智能缓存管理
  const cacheConfig = ref({
    maxSize: 50,
    ttl: 10 * 60 * 1000, // 10分钟
    autoCleanup: true,
    compressionEnabled: true
  })

  // 操作状态跟踪
  const operationMetrics = ref({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    lastOperationTime: null
  })

  // 注意：规则明细功能已迁移到 ruleDetails.js Store

  // ==================== 增强计算属性 ====================

  // 基础统计
  const rulesCount = computed(() => rules.value?.length || 0)
  const hasRules = computed(() => (rules.value?.length || 0) > 0)
  const isLoading = computed(() => loading.value || detailLoading.value)

  // 增强加载状态
  const loadingState = computed(() => ({
    isLoading: isLoading.value,
    loadingType: loading.value ? 'list' : detailLoading.value ? 'detail' : 'none',
    canOperate: !isLoading.value
  }))

  // 按状态分组的规则
  const rulesByStatus = computed(() => {
    const statusGroups = {}
    if (rules.value && Array.isArray(rules.value)) {
      rules.value.forEach(rule => {
        const status = rule?.status || 'UNKNOWN'
        if (!statusGroups[status]) {
          statusGroups[status] = []
        }
        statusGroups[status].push(rule)
      })
    }
    return statusGroups
  })

  // 状态统计 - 支持五个统计量：总计、新增、逻辑变更、就绪、已废弃
  const statusCounts = computed(() => {
    // 初始化所有状态为0，确保显示完整的统计信息
    const counts = {
      'TOTAL': 0,      // 总计
      'NEW': 0,        // 新增
      'CHANGED': 0,    // 逻辑变更
      'READY': 0,      // 就绪
      'DEPRECATED': 0  // 已废弃
    }

    if (rules.value && Array.isArray(rules.value)) {
      // 计算总数
      counts.TOTAL = rules.value.length

      // 按状态统计
      rules.value.forEach(rule => {
        const status = rule?.status
        if (status && counts.hasOwnProperty(status)) {
          counts[status]++
        }
      })
    }

    return counts
  })

  // 可用状态列表
  const availableStatuses = computed(() => {
    return Object.keys(statusCounts.value).sort()
  })

  // 增强规则状态摘要
  const rulesSummary = computed(() => ({
    total: rulesCount.value,
    ready: statusCounts.value.READY || 0,
    new: statusCounts.value.NEW || 0,
    changed: statusCounts.value.CHANGED || 0,
    deprecated: statusCounts.value.DEPRECATED || 0,
    lastUpdate: lastFetchTime.value,
    healthScore: rulesCount.value > 0 ?
      ((statusCounts.value.READY || 0) + (statusCounts.value.CHANGED || 0)) / rulesCount.value * 100 : 100
  }))

  // 当前规则是否有效
  const hasCurrentRule = computed(() => {
    return currentRule.value?.rule_key
  })

  // 缓存性能指标
  const cachePerformance = computed(() => ({
    rulesHitRate: operationMetrics.value.totalRequests > 0 ?
      (operationMetrics.value.successfulRequests / operationMetrics.value.totalRequests * 100).toFixed(2) : '100.00',
    cacheSize: rulesCache.value.size + schemaCache.value.size + statsCache.value.size,
    maxCacheSize: cacheConfig.value.maxSize * 3,
    cacheUtilization: ((rulesCache.value.size + schemaCache.value.size + statsCache.value.size) / (cacheConfig.value.maxSize * 3) * 100).toFixed(2),
    averageResponseTime: operationMetrics.value.averageResponseTime.toFixed(2)
  }))

  // 系统健康状态
  const systemHealth = computed(() => {
    const successRate = operationMetrics.value.totalRequests > 0 ?
      operationMetrics.value.successfulRequests / operationMetrics.value.totalRequests : 1

    return {
      status: successRate >= 0.95 ? 'healthy' : successRate >= 0.8 ? 'warning' : 'critical',
      score: Math.round(successRate * 100),
      metrics: {
        successRate: (successRate * 100).toFixed(2),
        totalRequests: operationMetrics.value.totalRequests,
        failedRequests: operationMetrics.value.failedRequests
      }
    }
  })

  // 注意：规则明细相关计算属性已迁移到 ruleDetails.js Store

  // ==================== 智能缓存管理 ====================

  // 缓存清理策略
  const cleanupCache = () => {
    const now = Date.now()
    const ttl = cacheConfig.value.ttl

    // 清理过期的规则缓存
    for (const [key, value] of rulesCache.value.entries()) {
      if (now - value.timestamp > ttl) {
        rulesCache.value.delete(key)
      }
    }

    // 清理过期的Schema缓存
    for (const [key, value] of schemaCache.value.entries()) {
      if (now - value.timestamp > ttl) {
        schemaCache.value.delete(key)
      }
    }

    // 清理过期的统计缓存
    for (const [key, value] of statsCache.value.entries()) {
      if (now - value.timestamp > ttl) {
        statsCache.value.delete(key)
      }
    }

    // 如果缓存仍然过大，清理最旧的条目
    const totalSize = rulesCache.value.size + schemaCache.value.size + statsCache.value.size
    if (totalSize > cacheConfig.value.maxSize * 3) {
      // 清理最旧的规则缓存
      const sortedRules = Array.from(rulesCache.value.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)

      const toRemove = Math.ceil(sortedRules.length * 0.3) // 清理30%
      for (let i = 0; i < toRemove; i++) {
        rulesCache.value.delete(sortedRules[i][0])
      }
    }
  }

  // 状态监听
  watch(
    () => operationMetrics.value.totalRequests,
    (newCount) => {
      // 每100次请求自动清理一次缓存
      if (newCount > 0 && newCount % 100 === 0 && cacheConfig.value.autoCleanup) {
        cleanupCache()
      }
    }
  )

  // 性能指标更新
  const updateMetrics = (success, responseTime) => {
    operationMetrics.value.totalRequests++
    operationMetrics.value.lastOperationTime = Date.now()

    if (success) {
      operationMetrics.value.successfulRequests++
    } else {
      operationMetrics.value.failedRequests++
    }

    // 计算平均响应时间
    const currentAvg = operationMetrics.value.averageResponseTime
    const totalRequests = operationMetrics.value.totalRequests
    operationMetrics.value.averageResponseTime =
      (currentAvg * (totalRequests - 1) + responseTime) / totalRequests
  }

  // ==================== 增强操作方法 ====================

  /**
   * 增强规则列表获取
   * 集成企业级状态管理、智能缓存和性能监控
   * @param {boolean} forceRefresh - 是否强制刷新，忽略缓存
   * @returns {Promise<Array>} 规则列表
   */
  const fetchRules = async (forceRefresh = false) => {
    const startTime = Date.now()

    // 智能缓存检查
    if (!forceRefresh && (rules.value?.length || 0) > 0 && lastFetchTime.value) {
      const timeDiff = Date.now() - lastFetchTime.value
      if (timeDiff < cacheConfig.value.ttl) {
        updateMetrics(true, 0) // 缓存命中
        return rules.value
      }
    }

    try {
      // 启动状态机
      await stateMachine.start()

      loading.value = true
      appStore.addLoadingTask('rules_fetch')

      const response = await getRulesStatus()
      const responseTime = Date.now() - startTime

      // 处理统一API响应格式
      let rulesData = []
      if (response && typeof response === 'object') {
        if (response.success && response.data) {
          // 新的统一格式：{success: true, data: [...]}
          rulesData = response.data
        } else if (Array.isArray(response)) {
          // 旧格式：直接是数组
          rulesData = response
        } else {
          console.warn('未知的API响应格式:', response)
          rulesData = []
        }
      }

      // 统一数据转换层：将后端字段映射为前端期望的字段
      rules.value = (rulesData || []).map(rule => ({
        ...rule,
        rule_name: rule.name || rule.rule_key, // 将 name 字段映射为 rule_name
        rule_key: rule.rule_key,
        status: rule.status,
        description: rule.description,
        updated_at: rule.updated_at
      }))
      lastFetchTime.value = Date.now()

      // 智能缓存更新
      if (rules.value && Array.isArray(rules.value)) {
        rules.value.forEach(rule => {
          if (rule?.rule_key) {
            rulesCache.value.set(rule.rule_key, {
              ...rule,
              timestamp: Date.now()
            })
          }
        })
      }

      // 成功状态
      await stateMachine.success()
      updateMetrics(true, responseTime)
      feedback.toastSuccess(`成功获取 ${rules.value.length} 条规则`)

      console.log('规则列表获取成功:', {
        count: rules.value.length,
        format: response?.success ? 'unified' : 'legacy',
        responseTime: `${responseTime}ms`,
        cacheSize: rulesCache.value.size
      })

      return rules.value
    } catch (error) {
      console.error('获取规则列表失败:', error)
      ElMessage.error('获取规则列表失败')

      // 如果有缓存数据，使用缓存
      if ((rules.value?.length || 0) === 0 && rulesCache.value.size > 0) {
        rules.value = Array.from(rulesCache.value.values())
        ElMessage.warning('网络异常，显示缓存数据')
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取规则详情
   * @param {string} ruleKey - 规则键
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 规则详情对象
   */
  const fetchRuleDetail = async (ruleKey, useCache = true) => {
    if (!ruleKey) return null

    // 检查缓存
    if (useCache) {
      const cachedRule = rulesCache.value.get(ruleKey)
      const cachedSchema = schemaCache.value.get(ruleKey)
      const cachedStats = statsCache.value.get(ruleKey)

      if (cachedRule && cachedSchema && cachedStats) {
        currentRule.value = cachedRule
        ruleSchema.value = cachedSchema
        ruleStatistics.value = cachedStats
        return { detail: cachedRule, schema: cachedSchema, statistics: cachedStats }
      }
    }

    detailLoading.value = true
    try {
      const [detail, schema, statistics] = await Promise.allSettled([
        getRuleDetail(ruleKey),
        getRuleSchema(ruleKey),
        getRuleStatistics(ruleKey)
      ])

      // 处理规则详情
      if (detail.status === 'fulfilled') {
        currentRule.value = detail.value
        rulesCache.value.set(ruleKey, detail.value)
      } else {
        console.warn('获取规则详情失败:', detail.reason)
        currentRule.value = rules.value.find(r => r.rule_key === ruleKey) || null
      }

      // 处理规则Schema
      if (schema.status === 'fulfilled') {
        ruleSchema.value = schema.value || []
        schemaCache.value.set(ruleKey, schema.value || [])
      } else {
        console.warn('获取规则Schema失败:', schema.reason)
        ruleSchema.value = schemaCache.value.get(ruleKey) || []
      }

      // 处理统计信息
      if (statistics.status === 'fulfilled') {
        ruleStatistics.value = statistics.value || {}
        statsCache.value.set(ruleKey, statistics.value || {})
      } else {
        console.warn('获取规则统计失败:', statistics.reason)
        ruleStatistics.value = statsCache.value.get(ruleKey) || {}
      }

      return {
        detail: currentRule.value,
        schema: ruleSchema.value,
        statistics: ruleStatistics.value
      }
    } catch (error) {
      console.error('获取规则详情失败:', error)
      ElMessage.error('获取规则详情失败')
      throw error
    } finally {
      detailLoading.value = false
    }
  }

  /**
   * 下载规则模板
   * @param {Object} rule - 规则对象
   * @returns {Promise<void>}
   */
  const downloadTemplate = async (rule) => {
    try {
      await downloadRuleTemplate(rule.rule_key, rule.rule_name)
      ElMessage.success(`${rule.rule_name} 模板下载成功`)
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败')
      throw error
    }
  }

  /**
   * 提交规则数据
   * @param {string} ruleKey - 规则键
   * @param {Object} submissionData - 提交数据
   * @returns {Promise<Object>} 提交结果
   */
  const submitRuleData = async (ruleKey, submissionData) => {
    try {
      const result = await confirmRuleSubmission(ruleKey, submissionData)
      ElMessage.success('数据提交成功')

      // 更新本地规则状态和缓存
      await nextTick(() => {
        const ruleIndex = rules.value.findIndex(r => r.rule_key === ruleKey)
        if (ruleIndex !== -1) {
          const updatedRule = {
            ...rules.value[ruleIndex],
            status: 'READY',
            updated_at: new Date().toISOString()
          }
          rules.value[ruleIndex] = updatedRule
          rulesCache.value.set(ruleKey, updatedRule)
        }
      })

      return result
    } catch (error) {
      console.error('提交数据失败:', error)
      ElMessage.error('提交数据失败')
      throw error
    }
  }

  // ==================== 查询和筛选方法 ====================

  /**
   * 搜索规则（已废弃，建议使用useSearch Composable）
   * @deprecated 使用useSearch Composable替代
   */
  const searchRules = (keyword) => {
    if (!keyword) return rules.value

    const lowerKeyword = keyword.toLowerCase()
    return rules.value.filter(rule =>
      rule.rule_name?.toLowerCase().includes(lowerKeyword) ||
      rule.rule_key?.toLowerCase().includes(lowerKeyword)
    )
  }

  /**
   * 按状态筛选规则（已废弃，建议使用useSearch Composable）
   * @deprecated 使用useSearch Composable替代
   */
  const filterRulesByStatus = (status) => {
    if (!status || status === 'ALL') return rules.value
    return rules.value.filter(rule => rule.status === status)
  }

  /**
   * 根据规则键获取规则
   * @param {string} ruleKey - 规则键
   * @returns {Object|undefined} 规则对象
   */
  const getRuleByKey = (ruleKey) => {
    // 优先从缓存获取
    const cachedRule = rulesCache.value.get(ruleKey)
    if (cachedRule) return cachedRule

    return rules.value.find(rule => rule.rule_key === ruleKey)
  }

  // 注意：规则明细基础方法已迁移到 ruleDetails.js Store
  // 请使用 useRuleDetailsStore() 获取规则明细相关功能

  // ==================== 状态管理方法 ====================

  /**
   * 清除当前规则详情
   */
  const clearCurrentRule = () => {
    currentRule.value = null
    ruleSchema.value = []
    ruleStatistics.value = {}
  }

  /**
   * 刷新单个规则
   * @param {string} ruleKey - 规则键
   * @returns {Promise<void>}
   */
  const refreshRule = async (ruleKey) => {
    const ruleIndex = rules.value.findIndex(r => r.rule_key === ruleKey)
    if (ruleIndex === -1) return

    try {
      const updatedRule = await getRuleDetail(ruleKey)
      const newRule = { ...rules.value[ruleIndex], ...updatedRule }
      rules.value[ruleIndex] = newRule
      rulesCache.value.set(ruleKey, newRule)
    } catch (error) {
      console.error('刷新规则失败:', error)
    }
  }

  // ==================== 缓存管理方法 ====================

  /**
   * 清除所有缓存
   */
  const clearCache = () => {
    rulesCache.value.clear()
    schemaCache.value.clear()
    statsCache.value.clear()
  }

  /**
   * 清除指定规则的缓存
   * @param {string} ruleKey - 规则键
   */
  const clearRuleCache = (ruleKey) => {
    rulesCache.value.delete(ruleKey)
    schemaCache.value.delete(ruleKey)
    statsCache.value.delete(ruleKey)
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  const getCacheStats = () => ({
    rulesCount: rulesCache.value.size,
    schemaCount: schemaCache.value.size,
    statsCount: statsCache.value.size,
    totalSize: rulesCache.value.size + schemaCache.value.size + statsCache.value.size
  })

  // ==================== 重置和清理方法 ====================

  /**
   * 重置整个Store状态
   */
  const resetStore = () => {
    rules.value = []
    currentRule.value = null
    ruleSchema.value = []
    ruleStatistics.value = {}
    loading.value = false
    detailLoading.value = false
    lastFetchTime.value = null

    clearCache()
    // 注意：规则明细状态重置已迁移到 ruleDetails.js Store
  }

  // ==================== 返回公共接口 ====================

  return {
    // 基础状态
    rules,
    currentRule,
    ruleSchema,
    ruleStatistics,
    loading,
    detailLoading,
    lastFetchTime,

    // 增强计算属性
    rulesCount,
    hasRules,
    isLoading,
    loadingState,
    rulesByStatus,
    statusCounts,
    availableStatuses,
    rulesSummary,
    hasCurrentRule,
    cachePerformance,
    systemHealth,

    // 核心操作方法
    fetchRules,
    fetchRuleDetail,
    downloadTemplate,
    submitRuleData,

    // 注意：规则明细相关功能已迁移到 ruleDetails.js Store

    // 查询和筛选方法（已废弃，建议使用Composables）
    searchRules,
    filterRulesByStatus,
    getRuleByKey,

    // 状态管理方法
    clearCurrentRule,
    refreshRule,

    // 缓存管理方法
    clearCache,
    clearRuleCache,
    getCacheStats,

    // 增强管理方法
    resetStore,
    cleanupCache,
    updateMetrics,

    // 配置和指标
    cacheConfig,
    operationMetrics
  }
})
